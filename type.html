<!--
 * @Author: mokevip
 * @Date: 2020-09-14 09:24:16
 * @LastEditors: mokevip
 * @LastEditTime: 2020-09-14 11:00:40
 * @Description: 
-->
<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="Expires" content="0">
        <meta http-equiv="Pragma" content="no-cache">
        <meta http-equiv="Cache-control" content="no-cache,must-revalidate">
        <meta http-equiv="Cache" content="no-cache">
        <title>答题类型选择</title>
        <link rel="stylesheet" href="./css/iview.css">
        <script>
        
    </script>
        <style>
        .typeList {
            width: 95vw;
            margin: 2vw auto;
        }
        
        .typeList>div {
            margin-top: 2vw;
        }

        .contilor{

width: 50%;

margin: 5% auto;

}

.box{

display: flex;

flex-wrap: wrap;

}

.item{
    font-size: 0.6rem;
            color: white;
            border-radius: 15px;
            padding: 0px 5px;
            letter-spacing: 0.1rem;
            text-indent: 0.2rem;
            background-color: #747677;
            display: inline-block;
            margin-right: 6px;
            line-height: 20px;
}

.item1{
    font-size: 0.6rem;
            color: white;
            border-radius: 15px;
            padding: 0px 5px;
            letter-spacing: 0.1rem;
            text-indent: 0.2rem;
            background-color: #036232;
            display: inline-block;
            margin-right: 6px;
            line-height: 20px;
}

.type {
            font-size: 0.6rem;
            color: white;
            border-radius: 15px;
            padding: 0px 5px;
            letter-spacing: 0.1rem;
            text-indent: 0.2rem;
            background-color: #1890ff;
            display: inline-block;
            margin-right: 6px;
            line-height: 20px;
        }

    </style>

    </head>

    <body>
        <div id="el">
            <p style="margin-left:3vw;margin-top: 3vw;font-size: 0.9rem;">请选择答题模式：
                <label class='item' v-for="(item,i) in itemList" :key="i" :class="{'item1':item.select?true:false}" @click="onSelect1(i)">
                {{item.text}}
                </label>
            </p>  

            <div class="typeList">
                <div v-for="i in typeList" @click='goNext(i.id)'>
                    <card class="timu">
                        {{i.name}}
                    </card>
                </div>
            </div>
        </div>
        <script src="./js/vue.min.js"></script>
        <script src="./js/iview.min.js"></script>
        <script>
        const vue = new Vue({
            el: "#el",
            data: {
                itemList : [{
                    id: '1',
                    text: '打乱选项',
                    select: parseInt(sessionStorage.shuf_option)
                }],
                typeList: [{
                    id: 'random',
                    name: "乱序答题"
                }, {
                    id: 'order',
                    name: "顺序答题"
                }, {
                    id: 'recite',
                    name: "背题模式"
                }, {
                    id: 'wrong',
                    name: "错题模式"
                }, {
                    id: 'important',
                    name: "重点题目"
                }]
            },
            methods: {
                goNext(id) {
                    sessionStorage.type = id;
                    console.log(444,sessionStorage.shuf_option)
                    if (parseInt(sessionStorage.shuf_option)){
                        console.log(333,sessionStorage.shuf_option)
                        window.location.href = "./timu_s.html"; 
                        return;
                    };
                    window.location.href = "./timu.html";
                    
                },
                onSelect1(i) {
                    if (this.itemList[i].select) {this.itemList[i].select = 0}else{this.itemList[i].select=1}
                    // this.itemList[i].select = !this.itemList[i].select;
                    // if (this.itemList[i].select){console.log(111,this.itemList[i].select)}
                    sessionStorage.shuf_option = this.itemList[i].select;
                    // if (parseInt(sessionStorage.shuf_option)){console.log(222, sessionStorage.shuf_option, sessionStorage.shuf_option==='true')}
                }
            }
        })
    </script>
    </body>

</html>
