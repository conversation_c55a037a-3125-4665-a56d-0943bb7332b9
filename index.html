<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache,must-revalidate">
    <meta http-equiv="Cache" content="no-cache">
    <title>刷题</title>
    <link rel="stylesheet" href="./css/iview.css">
    <script>
        var _hmt = _hmt || [];
        (function() {
            var hm = document.createElement("script");
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>

    <style>
        .title {
            text-align: center;
            margin-top: 2vw;
        }
        
        .timuList {
            width: 96vw;
            margin: 2vw auto 0;
        }
        
        .timu {
            margin-top: 2vw;
        }
        
        .author {
            text-align: right;
            padding-right: 3.2vw;
            margin-bottom: 1.5vw;
            font-size: 7px;

        }
    </style>
</head>

<body>
    <div id="el">
        <h1 class='title'>刷题</h1>
        <div class="author"><a href="">@wuxl7</a></div>
        <div v-if="username" class="welcome">欢迎回来，{{username}}</div>
        
        <Modal v-model="showNameModal" title="请输入用户名" :closable="false" :mask-closable="false">
            <Input v-model="username" placeholder="请输入您的用户名" @on-enter="saveUsername" />
            <div slot="footer">
                <Button type="primary" @click="saveUsername" :disabled="!username.trim()">确定</Button>
            </div>
        </Modal>
        
        <div class="timuList">
            <div v-for="i in jsonList" @click='goNext(i.id,i.file)'>
                <Card class="timu">
                    <p slot="title">{{i.name}}</p>
                    {{i.describe}}
                </Card>
            </div>
        </div>
    </div>
    <script src="./js/public.js?version=1.0"></script>
    <script src="./js/vue.min.js"></script>
    <script src="./js/iview.min.js"></script>
    <script>
        const vue = new Vue({
            el: "#el",
            data: {
                jsonList: JSONList,
                showNameModal: false,
                username: ''
            },
            methods: {
                goNext: function(id, file) {
                    sessionStorage.id = id;
                    sessionStorage.file = file;
                    window.location.href = "./type.html"
                },
                checkUsername() {
                    if (!localStorage.getItem('username')) {
                        this.showNameModal = true;
                    }
                },
                saveUsername() {
                    if (this.username.trim()) {
                        localStorage.setItem('username', this.username);
                        this.showNameModal = false;
                    }
                }
            },
            mounted() {
                this.checkUsername();
                this.username = localStorage.getItem('username') || '';
            }
        })
    </script>
</body>

</html>
