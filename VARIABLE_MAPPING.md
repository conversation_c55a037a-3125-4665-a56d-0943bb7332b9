# 变量名映射表 - 混淆代码 vs 反混淆代码

## 核心函数和变量映射

### 主要导入变量
| 混淆名称 | 反混淆名称 | 用途 |
|---------|-----------|------|
| `Fd` | `fetchSeatDataAPI` | API调用函数，获取座位数据 |
| `Nd` | `dynamicImport` | 动态导入函数 |
| `E` | `deserializedData` | 反序列化后的数据 |
| `C` | `transformedSeats` | 转换后的座位数组 |
| `M` | `seatData` | 单个座位数据 |
| `$` | `coordinates` | 坐标数组 |
| `W` | `coord` | 单个坐标值 |

### fetchAvailSeatData2 函数内部变量
| 混淆名称 | 反混淆名称 | 类型 | 说明 |
|---------|-----------|------|------|
| `u` | `areaId` | string | 区域ID参数 |
| `i` | `resolve` | function | Promise resolve 函数 |
| `h` | `reject` | function | Promise reject 函数 |
| `g` | `binaryData` | Uint8Array | 服务器返回的二进制数据 |
| `d` | `protobufModule` | object | 动态导入的protobuf模块 |

### 座位数据字段映射
| Protobuf字段 | 混淆访问 | 反混淆含义 | 最终字段名 |
|-------------|---------|-----------|-----------|
| `a` | `E.a` | 区域名称 | `areaName` |
| `ai` | `E.ai` | 座位数组 | 遍历处理 |
| `b` | `M.b` | 座位名称 | `name` |
| `e` | `M.e` | 颜色代码 | `color` |
| `g` | `M.g` | 坐标字符串 | `coordsStr` |
| `i` | `M.i` | 排名 | `rowName` |
| `k` | `M.k` | 座位ID | `id` |
| `ai` | `M.ai` | 票价等级 | `fareLevel` |
| `x` | `M.x` | X坐标 | `x` |
| `y` | `M.y` | Y坐标 | `y` |

## 完整的反混淆对照

### 原始混淆代码
```javascript
fetchAvailSeatData2(u) {
    return new Promise((i, h) => {
        Fd({
            areaId: u,
            showId: this._sid
        }).then(g => {
            Nd(() => import("./onlineShowSeat_pb-26e209c4.js"), [...]).then(d => {
                const E = d.default.ShowAreaPb.deserializeBinary(g).toObject(),
                    C = E.ai.map(M => {
                        const $ = M.g.split(",").map(W => +W);
                        return {
                            coords: [$[0], $[1]],
                            coordsStr: M.g,
                            aid: u,
                            color: M.e,
                            fareLevel: M.ai,
                            id: M.k,
                            rowName: M.i,
                            name: M.b,
                            areaName: E.a,
                            x: M.x ? +M.x : void 0,
                            y: M.y ? +M.y : void 0
                        }
                    });
                i(C)
            })
        }).catch(g => {
            h(g)
        })
    })
}
```

### 反混淆后代码
```javascript
fetchAvailSeatData2(areaId) {
    return new Promise((resolve, reject) => {
        fetchSeatDataAPI({
            areaId: areaId,
            showId: this._sid
        }).then(binaryData => {
            dynamicImport(() => import("./onlineShowSeat_pb-26e209c4.js"), [...]).then(protobufModule => {
                const deserializedData = protobufModule.default.ShowAreaPb.deserializeBinary(binaryData).toObject(),
                    transformedSeats = deserializedData.ai.map(seatData => {
                        const coordinates = seatData.g.split(",").map(coord => +coord);
                        return {
                            coords: [coordinates[0], coordinates[1]],
                            coordsStr: seatData.g,
                            aid: areaId,
                            color: seatData.e,
                            fareLevel: seatData.ai,
                            id: seatData.k,
                            rowName: seatData.i,
                            name: seatData.b,
                            areaName: deserializedData.a,
                            x: seatData.x ? +seatData.x : undefined,
                            y: seatData.y ? +seatData.y : undefined
                        }
                    });
                resolve(transformedSeats)
            })
        }).catch(error => {
            reject(error)
        })
    })
}
```

## 其他重要函数映射

### Debounce 函数 (修复后)
| 混淆代码位置 | 问题 | 修复后 |
|-------------|------|--------|
| Line 5349-5353 | 模板字符串语法错误 | 正确的模板字符串语法 |
| `hf` | 函数名混淆 | `debounce` |
| `f` | 第一个参数 | `func` |
| `u` | 第二个参数 | `wait` |
| `i` | 第三个参数 | `options` |

## 导入的组件映射

### BarPicked 组件相关
| 混淆名称 | 反混淆名称 | 用途 |
|---------|-----------|------|
| `e0` | `ActionSheet` | 操作表单组件 |
| `n0` | `BarPicked` | 已选座位栏组件 |
| `t0` | `BarPay` | 支付栏组件 |

### BarPrice 组件相关
| 混淆名称 | 反混淆名称 | 用途 |
|---------|-----------|------|
| `di` | `Hammer` | 手势库 |
| `r0` | `Skeleton` | 骨架屏组件 |
| `i0` | `BarPrice` | 价格栏组件 |
| `zu` | `showToast` | 提示框函数 |

## 关键常量和配置

| 混淆名称 | 值 | 用途 |
|---------|---|------|
| `Je` | `2500` | 触摸事件超时时间(ms) |
| `re` | `25` | 触摸阈值 |
| `Me` | `25` | 速度计算间隔 |

## 总结

通过这个映射表，可以看出：

1. **函数参数**: 大多使用单字母变量名 (`u`, `i`, `h`, `g`, `d`)
2. **数据结构**: Protobuf 字段使用简短字母 (`a`, `b`, `e`, `g`, `i`, `k`)
3. **导入别名**: 使用随机字母组合 (`Fd`, `Nd`, `E`)
4. **组件名**: 使用字母数字组合 (`e0`, `n0`, `t0`)

这种混淆策略有效地隐藏了代码的真实意图，但通过分析数据流和上下文，仍然可以推断出各变量的实际用途。
