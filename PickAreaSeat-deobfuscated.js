/**
 * DEOBFUSCATED VERSION OF PickAreaSeat-6336db40.js
 * Focus: fetchAvailSeatData2 function and serialization/deserialization process
 */

// ===== IMPORTS AND DEPENDENCIES =====
// Main imports from index file
import {
    // ... other imports
    E as fetchSeatDataAPI,  // Original: Fd - API function to fetch seat data
    _ as dynamicImport,      // Original: Nd - Dynamic import function
    // ... other imports
} from "./index-8f5e60a7.js";

// Component imports
import {
    A as ActionSheet,
    _ as BarPicked,
    a as BarPay
} from "./BarPicked.vue_vue_type_script_setup_true_lang-0f67fa96.js";

import {
    H as Hammer,
    S as Skeleton,
    _ as BarPrice,
    s as showToast
} from "./BarPrice.vue_vue_type_script_setup_true_lang-0dcb8ca9.js";

// ===== MAIN CLASS/COMPONENT =====
class SeatAreaManager {
    constructor(showId) {
        this._sid = showId; // Show ID
        // ... other initialization
    }

    /**
     * MAIN FUNCTION: Fetch available seat data using Protocol Buffers
     * 
     * @param {string} areaId - The area ID to fetch seats for
     * @returns {Promise<Array>} Promise resolving to array of seat objects
     */
    fetchAvailSeatData2(areaId) {
        return new Promise((resolve, reject) => {
            // Step 1: Call API to get binary protobuf data
            fetchSeatDataAPI({
                areaId: areaId,
                showId: this._sid
            }).then(binaryData => {
                // Step 2: Dynamically import the protobuf definitions
                dynamicImport(
                    () => import("./onlineShowSeat_pb-26e209c4.js"), 
                    [
                        "assets/onlineShowSeat_pb-26e209c4.js", 
                        "assets/index-8f5e60a7.js", 
                        "assets/index-60227b01.css"
                    ]
                ).then(protobufModule => {
                    // Step 3: DESERIALIZATION PROCESS
                    // Use protobuf to deserialize binary data and convert to JavaScript object
                    const deserializedData = protobufModule.default.ShowAreaPb
                        .deserializeBinary(binaryData)  // Deserialize binary protobuf data
                        .toObject();                    // Convert to plain JavaScript object

                    // Step 4: TRANSFORM DATA
                    // Map the protobuf structure to application-friendly format
                    const transformedSeats = deserializedData.ai.map(seatData => {
                        // Parse coordinates from comma-separated string
                        const coordinates = seatData.g.split(",").map(coord => +coord);
                        
                        return {
                            // Seat positioning
                            coords: [coordinates[0], coordinates[1]], // [x, y] coordinates
                            coordsStr: seatData.g,                    // Original coordinate string
                            
                            // Seat identification
                            aid: areaId,                              // Area ID
                            id: seatData.k,                          // Seat ID
                            rowName: seatData.i,                     // Row name (e.g., "A", "B")
                            name: seatData.b,                        // Seat name/number
                            areaName: deserializedData.a,           // Area name
                            
                            // Seat properties
                            color: seatData.e,                       // Color code for seat status
                            fareLevel: seatData.ai,                 // Fare/price level
                            
                            // Optional positioning (if available)
                            x: seatData.x ? +seatData.x : undefined,
                            y: seatData.y ? +seatData.y : undefined
                        };
                    });

                    // Step 5: Return transformed data
                    resolve(transformedSeats);
                })
                .catch(error => {
                    reject(error);
                });
            })
            .catch(error => {
                reject(error);
            });
        });
    }

    /**
     * SERIALIZATION PROCESS EXPLANATION:
     * 
     * 1. SERVER SIDE:
     *    - Server has seat data in database/memory
     *    - Data is serialized using Protocol Buffers (protobuf) format
     *    - Protobuf creates compact binary representation
     *    - Binary data is sent over HTTP
     * 
     * 2. CLIENT SIDE (this function):
     *    - Receives binary protobuf data from API
     *    - Dynamically imports protobuf schema definitions
     *    - Uses ShowAreaPb.deserializeBinary() to parse binary data
     *    - Converts to JavaScript object with .toObject()
     *    - Transforms data structure for application use
     * 
     * PROTOBUF STRUCTURE (inferred):
     * message ShowAreaPb {
     *   string a = 1;        // Area name
     *   repeated SeatPb ai = 2;  // Array of seats
     * }
     * 
     * message SeatPb {
     *   string b = 1;        // Seat name
     *   string e = 2;        // Color/status
     *   string g = 3;        // Coordinates (comma-separated)
     *   string i = 4;        // Row name
     *   string k = 5;        // Seat ID
     *   int32 ai = 6;        // Fare level
     *   optional int32 x = 7; // X position
     *   optional int32 y = 8; // Y position
     * }
     */
}

// ===== UTILITY FUNCTIONS =====

/**
 * Helper function for debouncing (from original obfuscated code)
 */
function debounce(func, wait = 100, options = {}) {
    if (typeof func != "function") {
        throw new TypeError(`Expected the first parameter to be a function, got \`${typeof func}\`.`);
    }
    if (wait < 0) throw new RangeError("`wait` must not be negative.");
    
    const { immediate } = typeof options == "boolean" ? { immediate: options } : options;
    let timeout, args, context, timestamp, result;

    function later() {
        const last = Date.now() - timestamp;
        if (last < wait && last >= 0) {
            timeout = setTimeout(later, wait - last);
        } else {
            timeout = null;
            if (!immediate) {
                result = func.apply(context, args);
                context = args = null;
            }
        }
    }

    const debounced = function(...newArgs) {
        context = this;
        args = newArgs;
        timestamp = Date.now();
        const callNow = immediate && !timeout;
        if (!timeout) timeout = setTimeout(later, wait);
        if (callNow) {
            result = func.apply(context, args);
            context = args = null;
        }
        return result;
    };

    debounced.cancel = function() {
        if (timeout) {
            clearTimeout(timeout);
            timeout = null;
        }
        context = args = null;
    };

    debounced.flush = function() {
        if (timeout) {
            result = func.apply(context, args);
            context = args = null;
            clearTimeout(timeout);
            timeout = null;
        }
        return result;
    };

    return debounced;
}

export { SeatAreaManager, debounce };
